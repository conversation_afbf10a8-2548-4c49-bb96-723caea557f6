<!DOCTYPE html>
<html lang="de">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XennyGames</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(120deg, #181c2f 0%, #232526 50%, #414345 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 20px;
        }

        h1 {
            color: white;
            font-size: 2.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            margin: 0;
        }

        .twitch-login-btn {
            background: linear-gradient(45deg, #9146ff, #772ce8);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(145, 70, 255, 0.3);
        }

        .twitch-login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(145, 70, 255, 0.4);
            background: linear-gradient(45deg, #a970ff, #8b44f0);
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 15px;
            background: rgba(255, 255, 255, 0.15);
            padding: 10px 20px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .user-name {
            color: white;
            font-weight: 600;
            font-size: 1rem;
            margin-left: auto;
        }

        .admin-badge {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #333;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 700;
            box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .admin-panel {
            display: none;
            margin-bottom: 20px;
            background: rgba(0, 0, 0, 0.9);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .admin-panel h3 {
            margin-bottom: 15px;
            color: #333;
        }

        .admin-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .admin-controls input {
            background-color: #222;    
            color: white;
            flex: 1;
            min-width: 200px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .action-btn {
            background-color: #222; 
            color: #fff;            
            border: 1px solid #555;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .action-btn:hover {
            background: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .action-btn.spieleliste {
            border-color: #2196F3;
            color: #2196F3;
        }

        .action-btn.spieleliste:hover {
            background: #2196F3;
            color: white;
        }
        .boxart-category-select {
            border-radius: 6px;
            background-color: #222; 
            color: #fff;            
            border: 1px solid #555; 
        }

        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .game-box {
            background: rgba(12, 12, 12, 0.95);
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .box-header {
            text-align: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 3px solid;
        }

        .gespielt .box-header {
            border-color: #4CAF50;
            color: #4CAF50;
        }

        .wird-gespielt .box-header {
            border-color: #FF9800;
            color: #FF9800;
        }

        .spieleliste .box-header {
            border-color: #2196F3;
            color: #2196F3;
        }

        .game-count {
            font-size: 0.9rem;
            opacity: 0.7;
        }

        .game-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            padding: 10px;
            min-height: 300px;
            max-height: 600px;
            overflow-y: auto;
            scrollbar-color: #555 #1e1e1e;
            scrollbar-width: thin;
        }

        .game-container::-webkit-scrollbar {
            width: 8px;
        }

        .game-container::-webkit-scrollbar-track {
            background: #1e1e1e;
        }

        .game-container::-webkit-scrollbar-thumb {
            background-color: #555;
            border-radius: 4px;
            border: 2px solid #1e1e1e;
        }

        .game-boxart {
            background: rgba(0, 0, 0, 0.9);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .game-boxart:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }

        .boxart-image {
            width: 100%;
            height: 240px;
            object-fit: cover;
            border-bottom: 1px solid #eee;
            background: #f0f0f0;
        }

        .boxart-footer {
            padding: 10px;
            display: grid;
            grid-template-rows: auto auto auto;
            gap: 8px;
        }

        .boxart-name {
            font-weight: bold;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: #eee;
        }

        .boxart-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
        }

        .boxart-status {
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 11px;
        }

        .status-owned {
            background: #4CAF50;
            color: white;
        }

        .status-not-owned {
            background: #f44336;
            color: white;
        }

        .boxart-votes {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .boxart-vote-btn {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            padding: 2px;
            transition: all 0.2s ease;
            color: grey;
        }

        .boxart-vote-btn:hover {
            transform: scale(1.1);
        }

        .boxart-vote-btn.upvote.active {
            color: #ff4500;
        }

        .boxart-vote-btn.downvote.active {
            color: #7193ff;
        }

        .boxart-score {
            font-size: 12px;
            font-weight: bold;
            min-width: 20px;
            text-align: center;
        }

        .score-positive {
            color: #ff4500;
        }

        .score-negative {
            color: #7193ff;
        }

        .score-zero {
            color: #878a8c;
        }

        .igdb-search-results {
            display: none;
            margin-top: 15px;
        }

        .igdb-search-results h4 {
            margin-bottom: 10px;
            color: #333;
        }

        .igdb-results-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 10px;
        }

        .igdb-result {
            background: white;
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }

        .igdb-result:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .igdb-result img {
            width: 100%;
            height: 120px;
            object-fit: cover;
            background: #f0f0f0;
        }

        .igdb-result div {
            padding: 5px;
        }

        .igdb-result button {
            margin-top: 5px;
            width: 100%;
            padding: 3px;
            font-size: 11px;
            background: #2196F3;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        .game-boxart.dragging {
            opacity: 0.5;
            transform: scale(0.95);
            border: 2px dashed #2196F3;
        }

        .game-container.drop-target {
            background: rgba(33, 150, 243, 0.1);
            border: 2px dashed #2196F3;
        }

        .game-container.drop-target::after {
            content: "Spiel hier ablegen";
            display: block;
            text-align: center;
            color: #2196F3;
            padding: 10px;
        }

        .loading {
            text-align: center;
            color: white;
            font-size: 1.2rem;
            margin: 50px 0;
        }

        .error {
            background: rgba(244, 67, 54, 0.9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
            display: none;
        }

        .error h3 {
            margin-bottom: 10px;
        }

        .refresh-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: block;
            margin: 20px auto 0;
        }

        .refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .connection-status {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            padding: 5px 10px;
            border-radius: 15px;
            color: white;
            font-size: 14px;
            z-index: 1000;
        }
        
        .connection-status.connected {
            background: rgba(76, 175, 80, 0.7);
        }
        
        .connection-status.disconnected {
            background: rgba(244, 67, 54, 0.7);
        }

        @media (max-width: 768px) {
            .game-container {
                max-height: none;
                overflow-y: visible;
                padding: 5px;
            }

            .dashboard {
                grid-template-columns: 1fr;
                display: flex;
                flex-direction: column;
            }

            .game-box.spieleliste {
                order: -1;
            }

            .game-box.wird-gespielt {
                order: 0;
            }

            .game-box.gespielt {
                order: 1;
            }

            .boxart-image {
                height: 160px;
            }

            .header {
                flex-direction: column;
                text-align: center;
            }

            .user-section {
                flex-direction: column;
                gap: 10px;
            }
        }

        @media (max-width: 480px) {
            .game-boxart {
                font-size: 12px;
            }

            .boxart-footer {
                gap: 4px;
                padding: 8px;
            }

            .boxart-name {
                font-size: 12px;
            }

            .boxart-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
            }

            .boxart-votes {
                align-self: flex-end;
            }
        }

        .boxart-actions {
            display: grid;
            gap: 5px;
            margin-top: 5px;
        }

        .boxart-actions button {
            padding: 5px;
            font-size: 12px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .boxart-actions button:hover {
            transform: translateY(-1px);
            opacity: 0.9;
        }

        .boxart-vote-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .game-box:not(.spieleliste) .boxart-vote-btn {
            display: none;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🎮 XennyGames</h1>
            <div class="auth-section">
                <div id="login-section" class="login-section">
                    <button id="twitch-login-btn" class="twitch-login-btn">
                        🎮 Mit Twitch anmelden
                    </button>
                </div>
                <div id="user-section" class="user-section" style="display: none;">
                    <div class="user-info">
                        <img id="user-avatar" src="" alt="Avatar" class="user-avatar">
                        <span id="user-name" class="user-name"></span>
                        <span id="admin-badge" class="admin-badge" style="display: none;">👑 Admin</span>
                    </div>
                    <button id="logout-btn" class="logout-btn">Abmelden</button>
                </div>
            </div>
        </div>

        <div id="admin-panel" class="admin-panel">
            <h3>Admin Tools</h3>
            <div class="admin-controls">
                <input type="text" id="new-game-name" placeholder="Spielname">
                <button type="button" onclick="searchIGDB()" class="action-btn spieleliste">IGDB Suche</button>
                <button type="button" onclick="addGame()" class="action-btn spieleliste">Manuell hinzufügen</button>
            </div>
            <div id="igdb-search-results" class="igdb-search-results">
                <h4>IGDB Suchergebnisse</h4>
                <div id="igdb-results-container" class="igdb-results-container"></div>
            </div>
        </div>

        <div id="loading" class="loading">
            Lade Spiele...
        </div>

        <div id="error" class="error">
            <h3>Fehler beim Laden der Spiele</h3>
            <p id="error-message"></p>
            <button class="refresh-btn" onclick="loadGames()">Erneut versuchen</button>
        </div>

        <div id="dashboard" class="dashboard" style="display: none;">
            <div class="game-box gespielt" data-category="Gespielt">
                <div class="box-header">
                    <h2>✅ Gespielt</h2>
                    <div class="game-count" id="gespielt-count">0 Spiele</div>
                </div>
                <div id="gespielt-games" class="game-container"></div>
            </div>

            <div class="game-box wird-gespielt" data-category="Spielt gerade">
                <div class="box-header">
                    <h2>🎯 Wird gespielt</h2>
                    <div class="game-count" id="wird-gespielt-count">0 Spiele</div>
                </div>
                <div id="wird-gespielt-games" class="game-container"></div>
            </div>

            <div class="game-box spieleliste" data-category="Auf der Spieleliste">
                <div class="box-header">
                    <h2>📋 Auf der Spieleliste</h2>
                    <div class="game-count" id="spieleliste-count">0 Spiele</div>
                </div>
                <div id="spieleliste-games" class="game-container"></div>
            </div>
        </div>
    </div>    
    <div id="connection-status" class="connection-status disconnected">Verbindung getrennt</div>
    <script src="/socket.io/socket.io.js"></script>
    <script>
        let currentUser=null,gameData=null,isAdmin=!1,socket=null,reconnectAttempts=0;const MAX_RECONNECT_ATTEMPTS=5,RECONNECT_DELAY=3e3,loginSection=document.getElementById("login-section"),userSection=document.getElementById("user-section"),userAvatar=document.getElementById("user-avatar"),userName=document.getElementById("user-name"),adminBadge=document.getElementById("admin-badge"),logoutBtn=document.getElementById("logout-btn"),adminPanel=document.getElementById("admin-panel"),dashboard=document.getElementById("dashboard"),loading=document.getElementById("loading"),error=document.getElementById("error"),errorMessage=document.getElementById("error-message"),connectionStatus=document.getElementById("connection-status"),twitchLoginBtn=document.getElementById("twitch-login-btn");async function init(){setupEventListeners(),await checkSession(),connectToServer()}function setupEventListeners(){twitchLoginBtn.addEventListener("click",handleTwitchLogin),logoutBtn.addEventListener("click",logout)}async function checkSession(){try{const e=await fetch("/api/session");if(e.ok){setUser(await e.json())}}catch(e){console.log("No session found")}}function handleTwitchLogin(){const e=encodeURIComponent("openid");window.location=`https://id.twitch.tv/oauth2/authorize?client_id=******************************&redirect_uri=https://games.xenny.news/&response_type=token&scope=${e}&force_verify=true`}async function handleTwitchLoginToken(e){try{const t=await fetch("/api/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({token:e})});if(!t.ok){const e=await t.json();throw new Error(e.error||"Login failed")}setUser(await t.json()),window.location.hash=""}catch(e){console.error("Login failed:",e),showError(`Login fehlgeschlagen: ${e.message}`)}}function setUser(e){currentUser=e,isAdmin=e.isAdmin,loginSection.style.display="none",userSection.style.display="flex";let t=e.avatar;t=t?t.replace("http://","https://"):`https://static-cdn.jtvnw.net/jtv_user_pictures/${e.username}-profile_image-70x70.png`,userAvatar.onerror=function(){this.src="https://static-cdn.jtvnw.net/jtv_user_pictures/unknown-profile_image-70x70.png"},userAvatar.src=t,userName.textContent=e.display_name||e.username,e.isAdmin&&(adminBadge.style.display="inline-block",adminPanel.style.display="block"),renderGames()}async function logout(){try{await fetch("/api/logout",{method:"POST"})}catch(e){console.error("Logout failed:",e)}currentUser=null,isAdmin=!1,userSection.style.display="none",loginSection.style.display="block",adminBadge.style.display="none",adminPanel.style.display="none",renderGames()}function connectToServer(){socket&&socket.disconnect(),socket=io({reconnection:!0,reconnectionAttempts:MAX_RECONNECT_ATTEMPTS,reconnectionDelay:RECONNECT_DELAY,reconnectionDelayMax:5e3,randomizationFactor:.5}),socket.on("connect",(()=>{console.log("Connected to server"),reconnectAttempts=0,connectionStatus.textContent="Verbunden",connectionStatus.className="connection-status connected",hideError()})),socket.on("init",(e=>{gameData=e,renderGames()})),socket.on("update",(e=>{gameData=e,renderGames()})),socket.on("disconnect",(e=>{console.log("Disconnected:",e),connectionStatus.textContent="Verbindung getrennt",connectionStatus.className="connection-status disconnected","io server disconnect"===e&&setTimeout((()=>socket.connect()),RECONNECT_DELAY)})),socket.on("reconnect_attempt",(e=>{reconnectAttempts=e,connectionStatus.textContent=`Verbindung wird hergestellt... (${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`})),socket.on("reconnect_failed",(()=>{connectionStatus.textContent="Verbindung fehlgeschlagen. Seite neu laden.",showError("Verbindung zum Server verloren. Bitte Seite neu laden.")})),socket.on("connect_error",(e=>{console.error("Connection error:",e),showError(`Verbindungsfehler: ${e.message}`)}))}function renderGames(){if(!gameData)return loading.style.display="block",void(dashboard.style.display="none");loading.style.display="none",dashboard.style.display="grid",renderGameCategory("gespielt",gameData.games,"Gespielt"),renderGameCategory("wird-gespielt",gameData.games,"Spielt gerade"),renderGameCategory("spieleliste",gameData.games,"Auf der Spieleliste")}function renderGameCategory(e,t,n){const o=document.getElementById(`${e}-games`),a=document.getElementById(`${e}-count`);if(!o||!a)return;o.innerHTML="";const s=Object.entries(t).filter((([e,t])=>t.overlay===n));"Auf der Spieleliste"===n?s.sort(((e,t)=>{const n=gameData.votes[e[0]]?.score||0,o=gameData.votes[t[0]]?.score||0;return n!==o?o-n:t[1].createdAt.localeCompare(e[1].createdAt)})):s.sort(((e,t)=>t[1].createdAt.localeCompare(e[1].createdAt))),a.textContent=`${s.length} Spiel${1!==s.length?"e":""}`,s.forEach((([t,n])=>{const a=createGameElement(t,n,e);o.appendChild(a)}))}function createGameElement(e,t,n){const o=document.createElement("div");o.className="game-boxart",o.setAttribute("data-game",e),o.draggable=isAdmin;const a=gameData.votes[e]||{score:0,votes:{}},s=currentUser?a.votes[currentUser.username]:null,r=document.createElement("img");r.className="boxart-image",r.alt=e,r.onerror=function(){this.onerror=null,this.src="https://images.igdb.com/igdb/image/upload/t_cover_big/nocover.png"};const i=t.cover_url||"https://images.igdb.com/igdb/image/upload/t_cover_big/nocover.png";return r.src=i.startsWith("http")?i:"https://images.igdb.com/igdb/image/upload/t_cover_big/nocover.png",o.innerHTML=`\n                <div class="boxart-footer">\n                    <div class="boxart-name" title="${e}">${e}</div>\n                    \n                    <div class="boxart-meta">\n                        <span class="boxart-status ${t.owned?"status-owned":"status-not-owned"}">\n                            ${t.owned?"Gekauft":"Nicht Gekauft"}\n                        </span>\n                        \n                        ${"spieleliste"===n?`\n                            <div class="boxart-votes">\n                                <button class="boxart-vote-btn upvote ${"up"===s?"active":""}" \n                                        onclick="vote('${e.replace(/'/g,"\\'")}', 'up', event)"\n                                        ${currentUser?"":"disabled"}>\n                                    ▲\n                                </button>\n                                <span class="boxart-score ${getScoreClass(a.score)}">\n                                    ${a.score}\n                                </span>\n                                <button class="boxart-vote-btn downvote ${"down"===s?"active":""}" \n                                        onclick="vote('${e.replace(/'/g,"\\'")}', 'down', event)"\n                                        ${currentUser?"":"disabled"}>\n                                    ▼\n                                </button>\n                            </div>\n                        `:`\n                            <div class="boxart-votes">\n                                <span class="boxart-score ${getScoreClass(a.score)}">\n                                    ${a.score}\n                                </span>\n                            </div>\n                        `}\n                    </div>\n                    \n                    ${isAdmin?`\n                    <div class="boxart-actions">\n                        <select class="boxart-category-select" \n                                onchange="changeCategory('${e.replace(/'/g,"\\'")}', this.value)">\n                            <option value="Gespielt" ${"Gespielt"===t.overlay?"selected":""}>✅ Gespielt</option>\n                            <option value="Spielt gerade" ${"Spielt gerade"===t.overlay?"selected":""}>🎯 Spielt</option>\n                            <option value="Auf der Spieleliste" ${"Auf der Spieleliste"===t.overlay?"selected":""}>📋 Liste</option>\n                        </select>\n                        <button class="action-btn" onclick="toggleOwned('${e.replace(/'/g,"\\'")}', event)">\n                            Besitz ändern\n                        </button>\n                        <button class="action-btn" style="background: #f44336; color: white;" \n                                onclick="removeGame('${e.replace(/'/g,"\\'")}', event)">\n                            × Löschen\n                        </button>\n                    </div>\n                    `:""}\n                </div>\n            `,isAdmin&&(o.addEventListener("dragstart",handleDragStart),o.addEventListener("dragend",handleDragEnd)),o.insertBefore(r,o.firstChild),o}function getScoreClass(e){return e>0?"score-positive":e<0?"score-negative":"score-zero"}async function vote(e,t,n){if(n.stopPropagation(),currentUser)try{const n=gameData.votes[e]||{score:0,votes:{}},o=n.votes[currentUser.username]===t?null:t,a=await fetch("/api/vote",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({gameName:e,voteType:o})});if(!a.ok){const e=await a.json();throw new Error(e.error||"Vote failed")}null===o?delete n.votes[currentUser.username]:n.votes[currentUser.username]=o,n.score=Object.values(n.votes).reduce(((e,t)=>e+("up"===t?1:-1)),0);const s=document.querySelector(`.game-boxart[data-game="${e}"]`);if(s){const e=s.querySelector(".upvote"),t=s.querySelector(".downvote"),o=s.querySelector(".boxart-score");e.classList.toggle("active","up"===n.votes[currentUser.username]),t.classList.toggle("active","down"===n.votes[currentUser.username]),o.textContent=n.score,o.className=`boxart-score ${getScoreClass(n.score)}`}}catch(e){console.error("Vote failed:",e),alert(`Abstimmung fehlgeschlagen: ${e.message}`)}}async function addGame(){if(!isAdmin)return;const e=document.getElementById("new-game-name"),t=e.value.trim();if(t)try{await fetch("/api/games",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({game:{name:t,cover_url:"",owned:!1}})}),e.value=""}catch(e){console.error("Add game failed:",e),alert(`Spiel hinzufügen fehlgeschlagen: ${e.message}`)}else alert("Bitte Spielnamen eingeben")}async function changeCategory(e,t){if(isAdmin)try{await fetch("/api/game/update",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({gameName:e,updates:{overlay:t}})})}catch(e){console.error("Category change failed:",e),alert(`Kategorieänderung fehlgeschlagen: ${e.message}`)}}async function toggleOwned(e,t){if(t.stopPropagation(),isAdmin)try{const t=gameData.games[e]?.owned||!1;await fetch("/api/game/update",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({gameName:e,updates:{owned:!t}})})}catch(e){console.error("Owned toggle failed:",e),alert(`Besitzstatus ändern fehlgeschlagen: ${e.message}`)}}async function removeGame(e,t){if(t.stopPropagation(),isAdmin&&confirm(`Möchten Sie "${e}" wirklich entfernen?`))try{await fetch("/api/game/remove",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({gameName:e})})}catch(e){console.error("Remove game failed:",e),alert(`Spiel entfernen fehlgeschlagen: ${e.message}`)}}async function searchIGDB(){if(!isAdmin)return;const e=document.getElementById("new-game-name").value.trim();if(e)try{const t=await fetch("/api/igdb/search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:e})});if(!t.ok){const e=await t.json();throw new Error(e.error||"Search failed")}displayIGDBResults(await t.json())}catch(e){console.error("IGDB search failed:",e),alert(`Suche fehlgeschlagen: ${e.message}`)}}function displayIGDBResults(e){const t=document.getElementById("igdb-results-container"),n=document.getElementById("igdb-search-results");t.innerHTML="",n.style.display="block",e.forEach((e=>{const n=document.createElement("div");n.className="igdb-result";let o=e.cover_url||"https://images.igdb.com/igdb/image/upload/t_cover_big/nocover.png";o.startsWith("http")||(o="https://images.igdb.com/igdb/image/upload/t_cover_big/nocover.png"),n.innerHTML=`\n                    <img src="${o}" \n                         alt="${e.name}"\n                         onerror="this.onerror=null; this.src='https://images.igdb.com/igdb/image/upload/t_cover_big/nocover.png'">\n                    <div>\n                        <div>${e.name}</div>\n                        <button onclick="addIGDBGame('${e.name.replace(/'/g,"\\'")}', '${o}')">\n                            Hinzufügen\n                        </button>\n                    </div>\n                `,t.appendChild(n)}))}function addIGDBGame(e,t){document.getElementById("new-game-name").value=e,isAdmin&&fetch("/api/games",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({game:{name:e.trim(),cover_url:t,owned:!1}})}).then((e=>{if(!e.ok)throw new Error("Failed to add game");document.getElementById("igdb-search-results").style.display="none"})).catch((e=>{console.error("Add game failed:",e),alert(`Spiel hinzufügen fehlgeschlagen: ${e.message}`)}))}let draggedGame=null;function handleDragStart(e){isAdmin?(draggedGame=this.getAttribute("data-game"),this.classList.add("dragging"),e.dataTransfer.effectAllowed="move"):e.preventDefault()}function handleDragEnd(){this.classList.remove("dragging"),draggedGame=null}function handleDragOver(e){isAdmin&&(e.preventDefault(),this.classList.add("drop-target"))}function handleDragLeave(){isAdmin&&this.classList.remove("drop-target")}function handleDrop(e){if(e.preventDefault(),this.classList.remove("drop-target"),!draggedGame||!isAdmin)return;const t=this.parentElement.getAttribute("data-category");changeCategory(draggedGame,t)}function showError(e){error.style.display="block",errorMessage.textContent=e,dashboard.style.display="none",loading.style.display="none"}function hideError(){error.style.display="none"}document.addEventListener("DOMContentLoaded",init),document.querySelectorAll(".game-container").forEach((e=>{e.addEventListener("dragover",handleDragOver),e.addEventListener("dragleave",handleDragLeave),e.addEventListener("drop",handleDrop)})),window.addEventListener("load",(()=>{const e=window.location.hash.substring(1);if(e.includes("access_token")){handleTwitchLoginToken(e.split("=")[1].split("&")[0])}}));
    </script>
</body>
</html>